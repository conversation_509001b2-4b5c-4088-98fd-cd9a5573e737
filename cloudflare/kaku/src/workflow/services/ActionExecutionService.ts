/**
 * Clean Action Execution Service
 * Handles all action transformations and execution at the edge
 */

import { FormField, FormButton } from '../../form-generation/types';
import { Action, PageStateResult } from '../../agent/types/extract-result';
import { ElementCoordinateMapping } from '../../agent/services/coordinate-resolution';

export interface ExecutionPlan {
  userActions: Action[];      // Actions from user form input
  aiActions: AIAction[];      // Automatic AI actions (keep me signed in, etc.)
  submitAction?: Action;      // The submit button action
}

export interface AIAction {
  type: 'keep-me-signed-in' | 'remember-device' | 'stay-logged-in';
  field: FormField;
  coordinates?: { x: number; y: number };
  shouldExecute: boolean;     // Based on field.checked state
}

export interface FormSubmissionData {
  formValues: Record<string, string>;  // User input values
  clickedButtonId: string;             // Which button was clicked
  interaction: 'submit' | 'click';     // Type of interaction
}

export class ActionExecutionService {
  
  /**
   * Transform form submission into clean execution plan
   * This is the ONLY transformation point - everything else is execution
   */
  static createExecutionPlan(
    pageStateResult: PageStateResult,
    formSubmissionData: FormSubmissionData,
    coordinates: ElementCoordinateMapping
  ): ExecutionPlan {
    const { extractionResult } = pageStateResult;
    const { formValues, clickedButtonId, interaction } = formSubmissionData;

    // 1. Create AI actions (automatic, always executed if needed)
    const aiActions = this.createAIActions(extractionResult.controls.fields, coordinates);

    // 2. Create user actions (from form input)
    const userActions = this.createUserActions(
      extractionResult.controls.fields,
      formValues,
      coordinates
    );

    // 3. Create submit action
    const submitAction = this.createSubmitAction(
      extractionResult.controls.buttons,
      clickedButtonId,
      coordinates,
      interaction
    );

    return {
      userActions,
      aiActions,
      submitAction
    };
  }

  /**
   * Execute the complete plan in the correct order
   */
  static async executePlan(
    plan: ExecutionPlan,
    executor: ActionExecutor
  ): Promise<void> {
    // 1. Execute AI actions first (automatic behavior)
    for (const aiAction of plan.aiActions) {
      if (aiAction.shouldExecute) {
        await executor.executeAIAction(aiAction);
      }
    }

    // 2. Execute user actions (form filling)
    for (const action of plan.userActions) {
      await executor.executeUserAction(action);
    }

    // 3. Execute submit action last
    if (plan.submitAction) {
      await executor.executeUserAction(plan.submitAction);
    }
  }

  private static createAIActions(
    fields: FormField[],
    coordinates: ElementCoordinateMapping
  ): AIAction[] {
    const aiActions: AIAction[] = [];

    for (const field of fields) {
      if (!field.isDontAskAgainControl) continue;

      // Determine AI action type based on field characteristics
      let actionType: AIAction['type'] = 'keep-me-signed-in';
      if (field.label.toLowerCase().includes('remember')) {
        actionType = 'remember-device';
      } else if (field.label.toLowerCase().includes('stay')) {
        actionType = 'stay-logged-in';
      }

      aiActions.push({
        type: actionType,
        field,
        coordinates: coordinates[field.id],
        shouldExecute: !field.checked // Only execute if not already checked
      });
    }

    return aiActions;
  }

  private static createUserActions(
    fields: FormField[],
    formValues: Record<string, string>,
    coordinates: ElementCoordinateMapping
  ): Action[] {
    const actions: Action[] = [];

    for (const field of fields) {
      // Skip AI-controlled fields
      if (field.isDontAskAgainControl) continue;

      // Only include fields that have user input
      const value = formValues[field.name] || formValues[field.id];
      if (!value) continue;

      const fieldCoordinates = coordinates[field.id];
      if (!fieldCoordinates) continue;

      actions.push({
        type: field.actiontype as 'fill' | 'select',
        name: field.id,
        value,
        coordinates: fieldCoordinates,
        order: field.order,
        isSubmitAction: false
      });
    }

    return actions.sort((a, b) => a.order - b.order);
  }

  private static createSubmitAction(
    buttons: FormButton[],
    clickedButtonId: string,
    coordinates: ElementCoordinateMapping,
    interaction: string
  ): Action | undefined {
    if (interaction !== 'submit') return undefined;

    const button = buttons.find(b => b.id === clickedButtonId);
    if (!button) return undefined;

    const buttonCoordinates = coordinates[button.id];
    if (!buttonCoordinates) return undefined;

    return {
      type: 'click',
      name: button.id,
      value: '',
      coordinates: buttonCoordinates,
      order: button.order,
      isSubmitAction: true
    };
  }
}

/**
 * Interface for action execution
 * Implemented by the workflow to perform actual browser actions
 */
export interface ActionExecutor {
  executeAIAction(aiAction: AIAction): Promise<void>;
  executeUserAction(action: Action): Promise<void>;
}
