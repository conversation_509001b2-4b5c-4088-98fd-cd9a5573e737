/**
 * Prompt display component
 */

/**
 * Generate prompt display markup for verification codes and contextual information
 */
export function generatePromptDisplay(
  instruction: string | null,
  verificationCode: string | null,
): string {
  if (!instruction && !verificationCode) return '';

  const elements: string[] = [];

  if (verificationCode) {
    elements.push(`
        <div class="verification-code-value">${verificationCode}</div>
    `);
  }

  if (instruction) {
    elements.push(`
      <div class="contextual-info">
        <div class="contextual-info-text">${instruction}</div>
      </div>
    `);
  }

  return `<div class="prompt-container">${elements.join('')}</div>`;
}
