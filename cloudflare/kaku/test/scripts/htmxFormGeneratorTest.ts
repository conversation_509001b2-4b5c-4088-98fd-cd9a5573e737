/**
 * Test script for HTMX Form Generator
 * This script tests the integration between FORM_VISION_PROMPT and the HTMX form generator
 */

import { imageToBase64 } from '../common/ImageHelpers';
import { GeminiLLMRepository } from '../../src/llm/GeminiLLMRepository';
import { FORM_VISION_CLASSIFICATION_PROMPT_V34, FORM_VISION_PROMPT_V6 } from './geminiPerformance';
import { htmxFormGenerator, FormVisionResult } from '../../src/form-generation/htmx-generator';
import * as fs from 'fs';
import * as path from 'path';
import { makeParallelLLMCalls } from '../../src/llm/llm-parallel-calls';
import { LLMService } from '../../src/llm/LLMService';
import { LLMRequest } from '../../src/llm/types/llm-request';
import { ClassificationResult } from '../../src/form-generation/types';

const GEMINI_API_KEY = 'AIzaSyDzh7xuznZzDi0c0DCYegXqGDH3UxFF3DQ'; // TODO: ADD API KEY

/**
 * Generate HTML page with the HTMX form for visual testing
 */
function generateHTMLPage(
  htmxForm: string,
  llmResult: {
    extractionResult: FormVisionResult;
    classificationResult: ClassificationResult;
  },
): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - ${llmResult.classificationResult.screenInfo.title}</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">${llmResult.classificationResult.screenInfo.title}</h1>
                    <p class="text-base mb-2">${llmResult.classificationResult.screenInfo.description ?? ''}</p>
                </div>
                ${htmxForm}

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      ${JSON.stringify(llmResult, null, 2)}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>`;
}

// Removed duplicate HTML generation function - using unified approach

/**
 * Main test function - tests the simplified schema
 */
async function testHTMXFormGenerator() {
  if (!GEMINI_API_KEY) {
    console.log('Please insert your Gemini API key in the script');
    return;
  }

  const testImages = [
    '/files/screenshots/google-acknowledge.webp',
    '/files/screenshots/google_login.webp',
    '/files/screenshots/github_login.webp',
    '/files/screenshots/google_2step_verification.webp',
    '/files/screenshots/github_logged_in.webp',
    '/files/screenshots/passkey.webp',
  ];

  const workingFile = process.argv[2] ? `/files/screenshots/${process.argv[2]}` : testImages[1];
  const imageData = await imageToBase64(`${__dirname.replace('/scripts', workingFile)}`);
  const geminiRepository = new GeminiLLMRepository(
    GEMINI_API_KEY,
    'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio',
  );

  console.log('🔄 Testing HTMX Form Generator with simplified schema...');

  const llmRequest: Omit<LLMRequest, 'responseSchema' | 'prompt'> = {
    platform: 'google',
    screenshot: imageData,
    skipCache: true,
    viewportWidth: 1080,
    viewportHeight: 720,
    version: 'v1',
  };

  const llmService = new LLMService({
    primaryRepo: geminiRepository,
    secondaryRepo: geminiRepository,
  });
  const llmResult = await makeParallelLLMCalls(
    llmService,
    llmRequest,
    FORM_VISION_PROMPT_V6,
    FORM_VISION_CLASSIFICATION_PROMPT_V34,
  );

  const { classificationResult, extractionResult } = llmResult;

  try {
    const htmxForm = htmxFormGenerator.generateForm(extractionResult, classificationResult);

    console.log('✅ Successfully generated HTMX form');
    console.log(`   - Form length: ${htmxForm.length} characters`);
    console.log(`   - AuthState: ${extractionResult.screenInfo.authState}`);
    console.log(`   - Fields: ${extractionResult.controls.fields.length}`);
    console.log(`   - Buttons: ${extractionResult.controls.buttons.length}`);
    console.log(`   - ScreenClass: ${classificationResult.screenInfo.screenClass}`);

    const htmlPage = generateHTMLPage(htmxForm, {
      extractionResult: llmResult.extractionResult,
      classificationResult: llmResult.classificationResult,
    });
    const outputPath = path.join(__dirname, '../output/htmx-form-test.html');
    const outputDir = path.dirname(outputPath);

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, htmlPage);

    console.log('🎉 Test completed successfully!');
    console.log(`📁 HTML file saved to: ${outputPath}`);
  } catch (error) {
    console.error('❌ Failed to parse form vision result');
    console.error('Error:', error);
    console.error('Raw response:', llmResult);
  }
}

// Run the test
if (require.main === module) {
  testHTMXFormGenerator().catch(console.error);
}

export { testHTMXFormGenerator };
